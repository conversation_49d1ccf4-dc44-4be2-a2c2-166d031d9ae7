import CoreGraphics
import XCTest

@testable import OptimizedFusedSSIM

class OptimizedFusedSSIMTests: XCTestCase {

  var ssimCalculator: SSIMCalculator!
  var totalInitTime: Double = 0

  // MARK: - Test Assets

  // Test images (without alpha)
  let originImageJPG = "test_without_alpha_origin.jpg"
  let differentImageJPG = "test_without_alpha_different.jpg"
  let compressed1ImageJPG = "test_without_alpha_compressed-1.jpg"
  let compressed2ImageJPG = "test_without_alpha_compressed-2.jpg"

  // Test images (with alpha)
  let originImagePNG = "test_with_alpha_origin.png"
  let differentImagePNG = "test_with_alpha_different.png"
  let compressed1ImagePNG = "test_with_alpha_compressed-1.png"
  let compressed2ImagePNG = "test_with_alpha_compressed-2.png"

  override func setUpWithError() throws {
    try super.setUpWithError()
    let (_, time) = TestImageHelper.measureTime(operation: "模块初始化") {
      ssimCalculator = try! SSIMCalculator()
    }
    totalInitTime = time
    print("---")
  }

  override func tearDown() {
    ssimCalculator = nil
    super.tearDown()
    print("\n")
  }

  // MARK: - Stateless Tests

  func testStatelessWithoutAlpha() throws {
    print("🔬 [Stateless] 无 Alpha 通道测试开始")
    runStatelessTest(
      origin: originImageJPG,
      different: differentImageJPG,
      compressed1: compressed1ImageJPG,
      compressed2: compressed2ImageJPG
    )
  }

  func testStatelessWithAlpha() throws {
    print("🔬 [Stateless] 有 Alpha 通道测试开始")
    runStatelessTest(
      origin: originImagePNG,
      different: differentImagePNG,
      compressed1: compressed1ImagePNG,
      compressed2: compressed2ImagePNG
    )
  }

  // MARK: - Stateful Tests

  func testStatefulWithoutAlpha() throws {
    print("🔬 [Stateful] 无 Alpha 通道测试开始")
    runStatefulTest(
      origin: originImageJPG,
      compressed1: compressed1ImageJPG,
      compressed2: compressed2ImageJPG
    )
  }

  func testStatefulWithAlpha() throws {
    print("🔬 [Stateful] 有 Alpha 通道测试开始")
    runStatefulTest(
      origin: originImagePNG,
      compressed1: compressed1ImagePNG,
      compressed2: compressed2ImagePNG
    )
  }

  // MARK: - Helper Functions

  private func runStatelessTest(
    origin: String, different: String, compressed1: String, compressed2: String
  ) {
    // Test case 1: Compare with self
    executeAndMeasure(caseName: "计算与自己(相同图片)的 SSIM", image1Name: origin, image2Name: origin)

    // Test case 2: Compare with different image
    executeAndMeasure(caseName: "计算与不相图片的 SSIM", image1Name: origin, image2Name: different)

    // Test case 3: Compare with compressed image 1
    executeAndMeasure(caseName: "计算与压缩后的图片 1 的 SSIM", image1Name: origin, image2Name: compressed1)

    // Test case 4: Compare with compressed image 2
    executeAndMeasure(caseName: "计算与压缩后的图片 2 的 SSIM", image1Name: origin, image2Name: compressed2)
  }

  private func runStatefulTest(origin: String, compressed1: String, compressed2: String) {

    // Step 1: Set origin image
    print("\n--- 流程: 设置原图 ---")
    let (cgImage, ioTime) = TestImageHelper.measureTime(operation: "文件I/O") {
      TestImageHelper.loadImageFromBundle(named: origin)!
    }

    // In stateful mode, `setOrigin` includes the preprocessing time.
    let (_, preprocessingTime) = TestImageHelper.measureTime(operation: "转换+纹理创建") {
      try! ssimCalculator.setOrigin(image: cgImage)
    }

    print("图片预处理耗时 (文件I/O): \(String(format: "%.2f", ioTime))ms")
    print("图片预处理耗时 (转换+纹理创建): \(String(format: "%.2f", preprocessingTime))ms")
    print(String(format: "流程总计耗时: %.2fms", ioTime + preprocessingTime))

    // Step 2: Compare with compressed image 1
    print("\n--- 流程: 与压缩后的图片 1 对比 ---")
    let (cgImage1, ioTime1) = TestImageHelper.measureTime(operation: "文件I/O") {
      TestImageHelper.loadImageFromBundle(named: compressed1)!
    }
    // In stateful mode, `compareToOrigin` also includes preprocessing for the new image.
    // The SSIM calculation part is what we measure here.
    let (ssimResult1, ssimTime1) = TestImageHelper.measureTime(operation: "计算 SSIM (含新图预处理)") {
      try! ssimCalculator.compareToOrigin(with: cgImage1)
    }

    print("SSIM: \(ssimResult1.ssim)")
    print(String(format: "流程总计耗时: %.2fms", ioTime1 + ssimTime1))

    // Step 3: Compare with compressed image 2
    print("\n--- 流程: 与压缩后的图片 2 对比 ---")
    let (cgImage2, ioTime2) = TestImageHelper.measureTime(operation: "文件I/O") {
      TestImageHelper.loadImageFromBundle(named: compressed2)!
    }
    let (ssimResult2, ssimTime2) = TestImageHelper.measureTime(operation: "计算 SSIM (含新图预处理)") {
      try! ssimCalculator.compareToOrigin(with: cgImage2)
    }

    print("SSIM: \(ssimResult2.ssim)")
    print(String(format: "流程总计耗时: %.2fms", ioTime2 + ssimTime2))
  }

  private func executeAndMeasure(caseName: String, image1Name: String, image2Name: String) {
    print("\n--- 流程: \(caseName) ---")

    var ioTime: Double = 0

    let (image1, ioTime1) = TestImageHelper.measureTime(operation: "文件I/O (图1)") {
      TestImageHelper.loadImageFromBundle(named: image1Name)!
    }
    let (image2, ioTime2) = TestImageHelper.measureTime(operation: "文件I/O (图2)") {
      TestImageHelper.loadImageFromBundle(named: image2Name)!
    }
    ioTime = ioTime1 + ioTime2

    let (result, performance) = try! ssimCalculator.calculate(from: image1, to: image2)

    print("SSIM: \(result.ssim)")
    print(String(format: "图片预处理耗时 (文件I/O): %.2fms", ioTime))
    print(String(format: "图片预处理耗时 (转换+纹理创建): %.2fms", performance.preprocessingTime))
    print(String(format: "计算 SSIM 耗时: %.2fms", performance.ssimCalculationTime))
    print(
      String(
        format: "流程总计耗时: %.2fms",
        ioTime + performance.preprocessingTime + performance.ssimCalculationTime))
  }
}
