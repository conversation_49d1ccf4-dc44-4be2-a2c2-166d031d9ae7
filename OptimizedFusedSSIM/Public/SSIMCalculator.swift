//
//  SSIMCalculator.swift
//  OptimizedFusedSSIM
//
//  Created by <PERSON><PERSON> on 2025/7/7.
//

import CoreGraphics
import Metal

/// 主要的SSIM计算接口
public class SSIMCalculator {

    // MARK: - Performance Options

    /// 性能模式选择
    public enum PerformanceMode {
        case automatic  // 自动选择最佳模式
        case cpuOptimized  // CPU优化模式
        case gpuAccelerated  // GPU加速模式（Metal）
    }

    // MARK: - Internal Components

    private let imageProcessor: ImageProcessor
    private let metalSSIMKernel: MetalSSIMKernel
    private let performanceMode: PerformanceMode

    // MARK: - State for Stateful Operations
    private var originTexture: MTLTexture?
    private var originWidth: Int = 0
    private var originHeight: Int = 0

    // MARK: - Performance Report Structure
    public struct PerformanceReport {
        public let preprocessingTime: Double
        public let ssimCalculationTime: Double
    }

    // MARK: - Initialization

    /// 初始化SSIM计算器
    /// - Parameter performanceMode: 性能模式，默认为自动选择
    public init(performanceMode: PerformanceMode = .automatic) throws {
        self.performanceMode = performanceMode

        // 初始化统一的图像处理器
        self.imageProcessor = try ImageProcessor()

        // 初始化Metal SSIM内核
        self.metalSSIMKernel = try MetalSSIMKernel()

        print("✅ OptimizedFusedSSIM 初始化成功 (性能模式: \(performanceMode))")
    }

    // MARK: - Public SSIM Calculation Interface

    /// 计算两张图片之间的SSIM值 (带性能报告)
    /// - Parameters:
    ///   - image1: 第一张图片
    ///   - image2: 第二张图片
    /// - Returns: 一个元组，包含SSIM结果和详细的性能报告
    public func calculate(from image1: CGImage, to image2: CGImage) throws -> (
        result: SSIMResult, performance: PerformanceReport
    ) {
        guard image1.width == image2.width && image1.height == image2.height else {
            throw SSIMError.imageSizeMismatch
        }

        let (texture1, preprocessingTime1) = try imageProcessor.createTextureWithPerformance(
            from: image1)
        let (texture2, preprocessingTime2) = try imageProcessor.createTextureWithPerformance(
            from: image2)

        let (ssim, ssimTime) = try metalSSIMKernel.calculateWithPerformance(
            from: texture1,
            to: texture2
        )

        let ssimResult = SSIMResult(
            ssim: ssim, imageSize: .init(width: image1.width, height: image1.height))
        let performance = PerformanceReport(
            preprocessingTime: preprocessingTime1 + preprocessingTime2,
            ssimCalculationTime: ssimTime
        )

        return (ssimResult, performance)
    }

    /// 计算两张图片之间的SSIM值
    /// - Parameters:
    ///   - image1: 第一张图片
    ///   - image2: 第二张图片
    /// - Returns: SSIM结果
    public func calculate(from image1: CGImage, to image2: CGImage) throws -> SSIMResult {
        return try calculate(from: image1, to: image2).result
    }

    // MARK: - Stateful Interface

    /// 设置有状态比较的源图片
    /// - Parameter image: 源图片
    public func setOrigin(image: CGImage) throws {
        self.originTexture = try imageProcessor.createTexture(from: image)
        self.originWidth = image.width
        self.originHeight = image.height
    }

    /// 将新图片与已设置的源图片进行比较
    /// - Parameter image: 待比较的图片
    /// - Returns: SSIM结果
    public func compareToOrigin(with image: CGImage) throws -> SSIMResult {
        guard let originTexture = originTexture else {
            throw SSIMError.originNotSet
        }
        guard image.width == originWidth && image.height == originHeight else {
            throw SSIMError.imageSizeMismatch
        }

        let newTexture = try imageProcessor.createTexture(from: image)

        let ssim = try metalSSIMKernel.calculate(from: originTexture, to: newTexture)

        return SSIMResult(ssim: ssim, imageSize: .init(width: originWidth, height: originHeight))
    }

    // MARK: - Resource Management

    /// 清理所有缓存和临时资源，用于内存优化
    /// 建议在处理大量图像或内存敏感场景时调用
    public func clearCaches() {
        // 清理图像处理器的缓存
        // imageProcessor.clearCaches()

        // 清理Metal SSIM内核的缓存
        metalSSIMKernel.clearCaches()

        print("🧹 已清理所有缓存资源")
    }
}
