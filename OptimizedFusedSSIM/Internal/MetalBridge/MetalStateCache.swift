//
//  MetalStateCache.swift
//  OptimizedFusedSSIM
//
//  Created by <PERSON><PERSON> on 2025/7/7.
//

import Metal

/// Caches and manages reusable Metal state objects to avoid redundant creation.
internal final class MetalStateCache {

    /// The single, shared Metal device.
    let device: MTLDevice

    /// The command queue for all Metal operations.
    let commandQueue: MTLCommandQueue

    /// The compiled Metal shader library.
    let library: MTLLibrary

    /// A cache for the compute pipeline states.
    let fusedSSIMPipelineState: MTLComputePipelineState
    let reductionPipelineState: MTLComputePipelineState

    /// Initializes the cache by pre-compiling all necessary pipeline states.
    /// - Throws: `SSIMError.metalNotSupported` if a suitable Metal device cannot be found.
    ///           `SSIMError.metalError` if the command queue, shader library, or pipeline states cannot be created.
    init() throws {
        guard let device = MTLCreateSystemDefaultDevice() else {
            throw SSIMError.metalKernelError("Metal is not supported on this device.")
        }
        self.device = device

        guard let commandQueue = device.makeCommandQueue() else {
            throw SSIMError.metalKernelError("Failed to create MTLCommandQueue.")
        }
        self.commandQueue = commandQueue

        do {
            let bundle = Bundle(for: MetalStateCache.self)
            guard let libraryURL = bundle.url(forResource: "SSIMKernels", withExtension: "metallib")
            else {
                throw SSIMError.metalKernelError(
                    "SSIMKernels.metallib not found in the framework bundle.")
            }
            self.library = try device.makeLibrary(URL: libraryURL)

            // Pre-compile and cache the pipeline states
            self.fusedSSIMPipelineState = try Self.createPipelineState(
                for: "fusedSSIMKernel", device: device, library: library)
            self.reductionPipelineState = try Self.createPipelineState(
                for: "reductionAverageKernel", device: device, library: library)

        } catch {
            // If the error is already an SSIMError, rethrow it. Otherwise, wrap it.
            if error is SSIMError {
                throw error
            } else {
                throw SSIMError.metalKernelError(
                    "Failed to initialize Metal state: \(error.localizedDescription)")
            }
        }
    }

    /// A static helper to create a compute pipeline state.
    private static func createPipelineState(
        for functionName: String, device: MTLDevice, library: MTLLibrary
    ) throws -> MTLComputePipelineState {
        guard let kernelFunction = library.makeFunction(name: functionName) else {
            throw SSIMError.metalKernelError(
                "Kernel function '\(functionName)' not found in the library.")
        }

        do {
            return try device.makeComputePipelineState(function: kernelFunction)
        } catch {
            throw SSIMError.metalKernelError(
                "Failed to create compute pipeline state for '\(functionName)': \(error.localizedDescription)"
            )
        }
    }
}
