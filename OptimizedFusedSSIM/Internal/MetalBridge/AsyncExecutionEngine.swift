//
// AsyncExecutionEngine.swift
// OptimizedFusedSSIM
//
// 功能描述：异步执行引擎，基于Swift Actor实现非阻塞SSIM计算
// 优化阶段：第一阶段 - 核心架构重构
//

import Metal
import CoreGraphics
import Foundation

// MARK: - SSIM计算引擎

/// 基于Swift Actor的异步SSIM计算引擎
actor SSIMCalculationEngine {
    private let device: MTLDevice
    private let commandQueue: MTLCommandQueue
    private let textureManager: TextureManager
    private let stateCache: MetalStateCache
    private let hardwareDetector: HardwareDetector
    
    // 性能统计
    private var calculationCount = 0
    private var totalExecutionTime: Double = 0
    
    /// 初始化异步计算引擎
    /// - Parameter device: Metal设备
    /// - Throws: 初始化失败时抛出错误
    init(device: MTLDevice) throws {
        self.device = device
        
        guard let queue = device.makeCommandQueue() else {
            throw SSIMError.metalKernelError("Failed to create command queue")
        }
        self.commandQueue = queue
        
        self.textureManager = try TextureManager(device: device)
        self.stateCache = try MetalStateCache(device: device)
        self.hardwareDetector = HardwareDetector(device: device)
        
        print("✅ SSIMCalculationEngine 初始化成功 (设备: \(device.name))")
    }
    
    // MARK: - 公共异步接口
    
    /// 异步计算两张图像的SSIM值
    /// - Parameters:
    ///   - image1: 第一张图像
    ///   - image2: 第二张图像
    /// - Returns: SSIM计算结果
    /// - Throws: 计算过程中的错误
    func calculateSSIM(image1: CGImage, image2: CGImage) async throws -> SSIMResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // 验证图像尺寸
        guard image1.width == image2.width && image1.height == image2.height else {
            throw SSIMError.imageSizeMismatch
        }
        
        // 异步创建纹理
        async let texture1Task = textureManager.createTexture(from: image1)
        async let texture2Task = textureManager.createTexture(from: image2)
        
        let (texture1, texture2) = try await (texture1Task, texture2Task)
        
        // 异步执行计算
        let ssimValue = try await executeComputation(texture1: texture1, texture2: texture2)
        
        // 更新统计信息
        calculationCount += 1
        let executionTime = CFAbsoluteTimeGetCurrent() - startTime
        totalExecutionTime += executionTime
        
        return SSIMResult(
            ssim: Double(ssimValue),
            imageSize: ImageSize(width: image1.width, height: image1.height)
        )
    }
    
    /// 异步计算SSIM值并返回性能报告
    /// - Parameters:
    ///   - image1: 第一张图像
    ///   - image2: 第二张图像
    /// - Returns: SSIM结果和性能报告的元组
    /// - Throws: 计算过程中的错误
    func calculateSSIMWithPerformance(image1: CGImage, image2: CGImage) async throws -> (result: SSIMResult, time: Double) {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try await calculateSSIM(image1: image1, image2: image2)
        let executionTime = (CFAbsoluteTimeGetCurrent() - startTime) * 1000
        return (result, executionTime)
    }
    
    /// 获取性能统计信息
    var performanceStats: String {
        let avgTime = calculationCount > 0 ? totalExecutionTime / Double(calculationCount) * 1000 : 0
        return """
        计算次数: \(calculationCount)
        总执行时间: \(String(format: "%.2f", totalExecutionTime * 1000))ms
        平均执行时间: \(String(format: "%.2f", avgTime))ms
        硬件信息: \(device.name)
        """
    }
    
    /// 清理缓存和资源
    func clearCaches() {
        textureManager.clearCaches()
        print("🧹 SSIMCalculationEngine 缓存已清理")
    }
    
    // MARK: - 私有计算实现
    
    /// 执行异步GPU计算
    private func executeComputation(texture1: MTLTexture, texture2: MTLTexture) async throws -> Float {
        guard let commandBuffer = commandQueue.makeCommandBuffer() else {
            throw SSIMError.metalKernelError("Failed to create command buffer")
        }
        
        // 获取自适应线程配置
        let imageSize = CGSize(width: texture1.width, height: texture1.height)
        let threadConfig = hardwareDetector.optimalThreadConfig(for: imageSize)
        
        // 编码计算命令
        let resultBuffer = try encodeSSIMKernel(
            commandBuffer: commandBuffer,
            texture1: texture1,
            texture2: texture2,
            config: threadConfig
        )
        
        // 异步等待完成
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            commandBuffer.addCompletedHandler { buffer in
                if buffer.status == .completed {
                    continuation.resume()
                } else {
                    let error = buffer.error?.localizedDescription ?? "Unknown GPU error"
                    continuation.resume(throwing: SSIMError.metalKernelError("GPU execution failed: \(error)"))
                }
            }
            commandBuffer.commit()
        }
        
        // 提取结果
        return try extractResult(from: resultBuffer, pixelCount: texture1.width * texture1.height)
    }
    
    /// 编码SSIM计算内核
    private func encodeSSIMKernel(
        commandBuffer: MTLCommandBuffer,
        texture1: MTLTexture,
        texture2: MTLTexture,
        config: AdaptiveThreadConfig
    ) throws -> MTLBuffer {
        let width = texture1.width
        let height = texture1.height
        let pixelCount = width * height
        
        // 创建结果缓冲区
        let threadGroupCount = config.threadgroupsPerGrid.width * config.threadgroupsPerGrid.height
        guard let resultBuffer = device.makeBuffer(
            length: MemoryLayout<Float>.stride * threadGroupCount,
            options: .storageModeShared
        ) else {
            throw SSIMError.metalKernelError("Failed to create result buffer")
        }
        
        // 初始化结果缓冲区
        let resultPointer = resultBuffer.contents().bindMemory(to: Float.self, capacity: threadGroupCount)
        for i in 0..<threadGroupCount {
            resultPointer[i] = 0.0
        }
        
        // 计算RGB三个通道的SSIM值
        let ssimValues = try [0, 1, 2].map { channelIndex in
            try encodeSingleChannelSSIM(
                commandBuffer: commandBuffer,
                texture1: texture1,
                texture2: texture2,
                channelIndex: UInt32(channelIndex),
                config: config
            )
        }
        
        // 将三个通道的结果写入最终缓冲区
        let finalSSIM = min(ssimValues[0], ssimValues[1], ssimValues[2])
        resultPointer[0] = finalSSIM
        
        return resultBuffer
    }
    
    /// 编码单通道SSIM计算
    private func encodeSingleChannelSSIM(
        commandBuffer: MTLCommandBuffer,
        texture1: MTLTexture,
        texture2: MTLTexture,
        channelIndex: UInt32,
        config: AdaptiveThreadConfig
    ) throws -> Float {
        let width = texture1.width
        let height = texture1.height
        let pixelCount = width * height
        
        // 创建中间缓冲区
        guard let ssimMapBuffer = device.makeBuffer(
            length: MemoryLayout<Float>.stride * pixelCount,
            options: .storageModePrivate
        ) else {
            throw SSIMError.metalKernelError("Failed to create SSIM map buffer")
        }
        
        let threadGroupCount = config.threadgroupsPerGrid.width * config.threadgroupsPerGrid.height
        guard let reductionBuffer = device.makeBuffer(
            length: MemoryLayout<Float>.stride * threadGroupCount,
            options: .storageModeShared
        ) else {
            throw SSIMError.metalKernelError("Failed to create reduction buffer")
        }
        
        // 第一步：融合SSIM计算
        try encodeFusedSSIMPass(
            commandBuffer: commandBuffer,
            texture1: texture1,
            texture2: texture2,
            ssimMapBuffer: ssimMapBuffer,
            channelIndex: channelIndex,
            config: config
        )
        
        // 第二步：归约求平均
        try encodeReductionPass(
            commandBuffer: commandBuffer,
            ssimMapBuffer: ssimMapBuffer,
            resultBuffer: reductionBuffer,
            pixelCount: pixelCount,
            config: config
        )
        
        // 等待计算完成并返回结果
        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()
        
        return try extractResult(from: reductionBuffer, pixelCount: pixelCount)
    }
    
    /// 编码融合SSIM计算通道
    private func encodeFusedSSIMPass(
        commandBuffer: MTLCommandBuffer,
        texture1: MTLTexture,
        texture2: MTLTexture,
        ssimMapBuffer: MTLBuffer,
        channelIndex: UInt32,
        config: AdaptiveThreadConfig
    ) throws {
        guard let encoder = commandBuffer.makeComputeCommandEncoder() else {
            throw SSIMError.metalKernelError("Failed to create compute encoder")
        }
        
        var mutableChannelIndex = channelIndex
        var C1: Float = (0.01 * 1.0) * (0.01 * 1.0)
        var C2: Float = (0.03 * 1.0) * (0.03 * 1.0)
        
        encoder.setComputePipelineState(stateCache.fusedSSIMPipelineState)
        encoder.setTexture(texture1, index: 0)
        encoder.setTexture(texture2, index: 1)
        encoder.setBuffer(ssimMapBuffer, offset: 0, index: 0)
        encoder.setBytes(&C1, length: MemoryLayout<Float>.stride, index: 1)
        encoder.setBytes(&C2, length: MemoryLayout<Float>.stride, index: 2)
        encoder.setBytes(&mutableChannelIndex, length: MemoryLayout<UInt32>.stride, index: 3)
        
        encoder.dispatchThreadgroups(
            config.threadgroupsPerGrid,
            threadsPerThreadgroup: config.threadsPerThreadgroup
        )
        encoder.endEncoding()
    }
    
    /// 编码归约计算通道
    private func encodeReductionPass(
        commandBuffer: MTLCommandBuffer,
        ssimMapBuffer: MTLBuffer,
        resultBuffer: MTLBuffer,
        pixelCount: Int,
        config: AdaptiveThreadConfig
    ) throws {
        guard let encoder = commandBuffer.makeComputeCommandEncoder() else {
            throw SSIMError.metalKernelError("Failed to create reduction encoder")
        }
        
        var mutablePixelCount = UInt32(pixelCount)
        
        encoder.setComputePipelineState(stateCache.reductionPipelineState)
        encoder.setBuffer(ssimMapBuffer, offset: 0, index: 0)
        encoder.setBuffer(resultBuffer, offset: 0, index: 1)
        encoder.setBytes(&mutablePixelCount, length: MemoryLayout<UInt32>.stride, index: 2)
        
        let reductionThreads = MTLSize(width: min(256, pixelCount), height: 1, depth: 1)
        let reductionGroups = MTLSize(
            width: (pixelCount + reductionThreads.width - 1) / reductionThreads.width,
            height: 1,
            depth: 1
        )
        
        encoder.dispatchThreadgroups(reductionGroups, threadsPerThreadgroup: reductionThreads)
        encoder.endEncoding()
    }
    
    /// 从缓冲区提取最终结果
    private func extractResult(from buffer: MTLBuffer, pixelCount: Int) throws -> Float {
        guard pixelCount > 0 else { return 0.0 }
        
        let resultPointer = buffer.contents().bindMemory(to: Float.self, capacity: 1)
        return resultPointer[0]
    }
}

// MARK: - 扩展功能

extension SSIMCalculationEngine {
    /// 获取硬件信息
    var hardwareInfo: String {
        return hardwareDetector.hardwareProfile.description
    }
    
    /// 获取支持的优化特性
    var supportedOptimizations: [String] {
        return hardwareDetector.supportedOptimizations()
    }
}
