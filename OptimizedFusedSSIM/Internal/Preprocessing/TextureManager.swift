//
// TextureManager.swift
// OptimizedFusedSSIM
//
// 功能描述：零拷贝纹理管理系统
// 优化阶段：第一阶段 - 核心架构重构
//

import Metal
import CoreGraphics
import CoreVideo
import Foundation

// MARK: - 纹理管理器

/// 零拷贝纹理管理器，基于CVMetalTextureCache实现高效的纹理创建
class TextureManager {
    private let device: MTLDevice
    private var textureCache: CVMetalTextureCache?
    private var pixelBufferPool: CVPixelBufferPool?
    
    // 纹理缓存配置
    private let maxCachedTextures = 8
    private var cachedTextures: [String: MTLTexture] = [:]
    
    // 性能统计
    private var textureCreationCount = 0
    private var cacheHitCount = 0
    
    /// 初始化纹理管理器
    /// - Parameter device: Metal设备
    /// - Throws: 纹理缓存或像素缓冲池创建失败时抛出错误
    init(device: MTLDevice) throws {
        self.device = device
        try setupTextureCache()
        try setupPixelBufferPool()
        
        print("✅ TextureManager 初始化成功 (设备: \(device.name))")
    }
    
    // MARK: - 公共接口
    
    /// 从CGImage创建Metal纹理（零拷贝）
    /// - Parameter cgImage: 输入图像
    /// - Returns: Metal纹理
    /// - Throws: 纹理创建失败时抛出错误
    func createTexture(from cgImage: CGImage) throws -> MTLTexture {
        textureCreationCount += 1
        
        // 检查缓存
        let cacheKey = generateCacheKey(for: cgImage)
        if let cachedTexture = cachedTextures[cacheKey] {
            cacheHitCount += 1
            return cachedTexture
        }
        
        // 创建新纹理
        let texture = try createTextureInternal(from: cgImage)
        
        // 缓存纹理（如果缓存未满）
        if cachedTextures.count < maxCachedTextures {
            cachedTextures[cacheKey] = texture
        }
        
        return texture
    }
    
    /// 创建纹理并返回性能统计
    /// - Parameter cgImage: 输入图像
    /// - Returns: 纹理和创建耗时的元组
    /// - Throws: 纹理创建失败时抛出错误
    func createTextureWithPerformance(from cgImage: CGImage) throws -> (texture: MTLTexture, time: Double) {
        let startTime = CFAbsoluteTimeGetCurrent()
        let texture = try createTexture(from: cgImage)
        let elapsedTime = (CFAbsoluteTimeGetCurrent() - startTime) * 1000
        return (texture, elapsedTime)
    }
    
    /// 清理所有缓存
    func clearCaches() {
        cachedTextures.removeAll()
        print("🧹 TextureManager 缓存已清理")
    }
    
    /// 获取性能统计信息
    var performanceStats: String {
        let hitRate = textureCreationCount > 0 ? Double(cacheHitCount) / Double(textureCreationCount) * 100 : 0
        return """
        纹理创建次数: \(textureCreationCount)
        缓存命中次数: \(cacheHitCount)
        缓存命中率: \(String(format: "%.1f", hitRate))%
        当前缓存大小: \(cachedTextures.count)/\(maxCachedTextures)
        """
    }
    
    // MARK: - 私有实现
    
    /// 设置Metal纹理缓存
    private func setupTextureCache() throws {
        let result = CVMetalTextureCacheCreate(
            kCFAllocatorDefault,
            nil,
            device,
            nil,
            &textureCache
        )
        
        guard result == kCVReturnSuccess else {
            throw SSIMError.metalKernelError("Failed to create CVMetalTextureCache: \(result)")
        }
    }
    
    /// 设置像素缓冲池
    private func setupPixelBufferPool() throws {
        let poolAttributes: [String: Any] = [
            kCVPixelBufferPoolMinimumBufferCountKey as String: 3,
            kCVPixelBufferPoolMaximumBufferAgeKey as String: 0
        ]
        
        let pixelBufferAttributes: [String: Any] = [
            kCVPixelBufferPixelFormatTypeKey as String: kCVPixelFormatType_32BGRA,
            kCVPixelBufferWidthKey as String: 4096,
            kCVPixelBufferHeightKey as String: 4096,
            kCVPixelBufferMetalCompatibilityKey as String: true,
            kCVPixelBufferIOSurfacePropertiesKey as String: [:]
        ]
        
        let result = CVPixelBufferPoolCreate(
            kCFAllocatorDefault,
            poolAttributes as CFDictionary,
            pixelBufferAttributes as CFDictionary,
            &pixelBufferPool
        )
        
        guard result == kCVReturnSuccess else {
            throw SSIMError.metalKernelError("Failed to create CVPixelBufferPool: \(result)")
        }
    }
    
    /// 内部纹理创建实现
    private func createTextureInternal(from cgImage: CGImage) throws -> MTLTexture {
        let width = cgImage.width
        let height = cgImage.height
        
        // 1. 获取或创建像素缓冲区
        let pixelBuffer = try createPixelBuffer(width: width, height: height)
        
        // 2. 零拷贝写入图像数据
        try writeImageToPixelBuffer(cgImage: cgImage, pixelBuffer: pixelBuffer)
        
        // 3. 从像素缓冲区创建Metal纹理
        return try createTextureFromPixelBuffer(pixelBuffer)
    }
    
    /// 创建像素缓冲区
    private func createPixelBuffer(width: Int, height: Int) throws -> CVPixelBuffer {
        // 如果尺寸适合，使用池中的缓冲区
        if width <= 4096 && height <= 4096, let pool = pixelBufferPool {
            var pixelBuffer: CVPixelBuffer?
            let status = CVPixelBufferPoolCreatePixelBuffer(
                kCFAllocatorDefault,
                pool,
                &pixelBuffer
            )
            
            if status == kCVReturnSuccess, let buffer = pixelBuffer {
                return buffer
            }
        }
        
        // 创建自定义尺寸的像素缓冲区
        let pixelBufferAttributes: [String: Any] = [
            kCVPixelBufferPixelFormatTypeKey as String: kCVPixelFormatType_32BGRA,
            kCVPixelBufferWidthKey as String: width,
            kCVPixelBufferHeightKey as String: height,
            kCVPixelBufferMetalCompatibilityKey as String: true,
            kCVPixelBufferIOSurfacePropertiesKey as String: [:]
        ]
        
        var pixelBuffer: CVPixelBuffer?
        let status = CVPixelBufferCreate(
            kCFAllocatorDefault,
            width,
            height,
            kCVPixelFormatType_32BGRA,
            pixelBufferAttributes as CFDictionary,
            &pixelBuffer
        )
        
        guard status == kCVReturnSuccess, let buffer = pixelBuffer else {
            throw SSIMError.metalKernelError("Failed to create CVPixelBuffer: \(status)")
        }
        
        return buffer
    }
    
    /// 将CGImage数据写入像素缓冲区
    private func writeImageToPixelBuffer(cgImage: CGImage, pixelBuffer: CVPixelBuffer) throws {
        CVPixelBufferLockBaseAddress(pixelBuffer, [])
        defer { CVPixelBufferUnlockBaseAddress(pixelBuffer, []) }
        
        guard let baseAddress = CVPixelBufferGetBaseAddress(pixelBuffer) else {
            throw SSIMError.metalKernelError("Failed to get pixel buffer base address")
        }
        
        let bytesPerRow = CVPixelBufferGetBytesPerRow(pixelBuffer)
        let width = CVPixelBufferGetWidth(pixelBuffer)
        let height = CVPixelBufferGetHeight(pixelBuffer)
        
        guard let context = CGContext(
            data: baseAddress,
            width: width,
            height: height,
            bitsPerComponent: 8,
            bytesPerRow: bytesPerRow,
            space: CGColorSpaceCreateDeviceRGB(),
            bitmapInfo: CGImageAlphaInfo.noneSkipFirst.rawValue
        ) else {
            throw SSIMError.metalKernelError("Failed to create CGContext")
        }
        
        // 清除背景并绘制图像
        context.clear(CGRect(x: 0, y: 0, width: width, height: height))
        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: cgImage.width, height: cgImage.height))
    }
    
    /// 从像素缓冲区创建Metal纹理
    private func createTextureFromPixelBuffer(_ pixelBuffer: CVPixelBuffer) throws -> MTLTexture {
        guard let textureCache = textureCache else {
            throw SSIMError.metalKernelError("Texture cache not initialized")
        }
        
        let width = CVPixelBufferGetWidth(pixelBuffer)
        let height = CVPixelBufferGetHeight(pixelBuffer)
        
        var cvMetalTexture: CVMetalTexture?
        let result = CVMetalTextureCacheCreateTextureFromImage(
            kCFAllocatorDefault,
            textureCache,
            pixelBuffer,
            nil,
            .bgra8Unorm,
            width,
            height,
            0,
            &cvMetalTexture
        )
        
        guard result == kCVReturnSuccess,
              let metalTexture = cvMetalTexture,
              let texture = CVMetalTextureGetTexture(metalTexture) else {
            throw SSIMError.metalKernelError("Failed to create Metal texture from pixel buffer: \(result)")
        }
        
        return texture
    }
    
    /// 生成缓存键
    private func generateCacheKey(for cgImage: CGImage) -> String {
        return "\(cgImage.width)x\(cgImage.height)_\(cgImage.hashValue)"
    }
}

// MARK: - 扩展功能

extension TextureManager {
    /// 预热纹理缓存
    /// - Parameter imageSizes: 预期的图像尺寸列表
    func warmupCache(for imageSizes: [CGSize]) {
        print("🔥 预热纹理缓存，尺寸数量: \(imageSizes.count)")
        // 这里可以预创建一些常用尺寸的像素缓冲区
    }
    
    /// 检查设备兼容性
    var isDeviceCompatible: Bool {
        return textureCache != nil && pixelBufferPool != nil
    }
}
