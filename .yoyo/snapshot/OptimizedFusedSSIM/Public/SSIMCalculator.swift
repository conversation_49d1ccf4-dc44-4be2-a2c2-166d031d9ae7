//
// SSIMCalculator.swift
// OptimizedFusedSSIM
//
// 功能描述：重构的SSIM计算器，使用新的异步执行引擎
// 优化阶段：第一阶段 - 核心架构重构
//

import CoreGraphics
import Metal

/// 主要的SSIM计算接口，基于异步执行引擎重构
public class SSIMCalculator {

    // MARK: - Performance Options

    /// 性能模式选择
    public enum PerformanceMode {
        case automatic      // 自动选择最佳模式
        case cpuOptimized   // CPU优化模式（保留用于小图像）
        case gpuAccelerated // GPU加速模式（Metal异步引擎）
    }

    // MARK: - Internal Components

    private let asyncEngine: SSIMCalculationEngine
    private let performanceMode: PerformanceMode
    private let device: MTLDevice

    // MARK: - State for Stateful Operations
    private var originImage: CGImage?
    private var originWidth: Int = 0
    private var originHeight: Int = 0

    // MARK: - Performance Report Structure
    public struct PerformanceReport {
        public let preprocessingTime: Double
        public let ssimCalculationTime: Double
        public let totalTime: Double
        public let hardwareInfo: String

        public init(preprocessingTime: Double, ssimCalculationTime: Double, hardwareInfo: String) {
            self.preprocessingTime = preprocessingTime
            self.ssimCalculationTime = ssimCalculationTime
            self.totalTime = preprocessingTime + ssimCalculationTime
            self.hardwareInfo = hardwareInfo
        }
    }

    // MARK: - Initialization

    /// 初始化SSIM计算器
    /// - Parameter performanceMode: 性能模式，默认为自动选择
    /// - Throws: 初始化失败时抛出错误
    public init(performanceMode: PerformanceMode = .automatic) throws {
        self.performanceMode = performanceMode

        // 获取Metal设备
        guard let metalDevice = MTLCreateSystemDefaultDevice() else {
            throw SSIMError.metalKernelError("Metal is not supported on this device")
        }
        self.device = metalDevice

        // 初始化异步执行引擎
        self.asyncEngine = try SSIMCalculationEngine(device: metalDevice)

        print("✅ OptimizedFusedSSIM 初始化成功 (性能模式: \(performanceMode), 设备: \(metalDevice.name))")
    }

    // MARK: - Public SSIM Calculation Interface

    /// 异步计算两张图片之间的SSIM值 (带性能报告)
    /// - Parameters:
    ///   - image1: 第一张图片
    ///   - image2: 第二张图片
    /// - Returns: 一个元组，包含SSIM结果和详细的性能报告
    public func calculate(from image1: CGImage, to image2: CGImage) async throws -> (
        result: SSIMResult, performance: PerformanceReport
    ) {
        let startTime = CFAbsoluteTimeGetCurrent()

        // 使用异步引擎计算SSIM
        let (result, executionTime) = try await asyncEngine.calculateSSIMWithPerformance(
            image1: image1,
            image2: image2
        )

        let totalTime = (CFAbsoluteTimeGetCurrent() - startTime) * 1000
        let hardwareInfo = await asyncEngine.hardwareInfo

        let performance = PerformanceReport(
            preprocessingTime: totalTime - executionTime,
            ssimCalculationTime: executionTime,
            hardwareInfo: hardwareInfo
        )

        return (result, performance)
    }

    /// 异步计算两张图片之间的SSIM值
    /// - Parameters:
    ///   - image1: 第一张图片
    ///   - image2: 第二张图片
    /// - Returns: SSIM结果
    public func calculate(from image1: CGImage, to image2: CGImage) async throws -> SSIMResult {
        return try await asyncEngine.calculateSSIM(image1: image1, image2: image2)
    }

    /// 同步计算接口（向后兼容）
    /// - Parameters:
    ///   - image1: 第一张图片
    ///   - image2: 第二张图片
    /// - Returns: SSIM结果
    /// - Note: 此方法使用RunLoop等待异步计算完成，建议使用异步版本
    public func calculateSync(from image1: CGImage, to image2: CGImage) throws -> SSIMResult {
        var result: SSIMResult?
        var error: Error?
        let semaphore = DispatchSemaphore(value: 0)

        Task {
            do {
                result = try await calculate(from: image1, to: image2)
            } catch {
                error = error
            }
            semaphore.signal()
        }

        semaphore.wait()

        if let error = error {
            throw error
        }

        guard let finalResult = result else {
            throw SSIMError.metalKernelError("Synchronous calculation failed")
        }

        return finalResult
    }

    // MARK: - Stateful Interface

    /// 设置有状态比较的源图片
    /// - Parameter image: 源图片
    public func setOrigin(image: CGImage) {
        self.originImage = image
        self.originWidth = image.width
        self.originHeight = image.height
    }

    /// 异步将新图片与已设置的源图片进行比较
    /// - Parameter image: 待比较的图片
    /// - Returns: SSIM结果
    public func compareToOrigin(with image: CGImage) async throws -> SSIMResult {
        guard let originImage = originImage else {
            throw SSIMError.originNotSet
        }
        guard image.width == originWidth && image.height == originHeight else {
            throw SSIMError.imageSizeMismatch
        }

        return try await asyncEngine.calculateSSIM(image1: originImage, image2: image)
    }

    /// 同步版本的源图片比较（向后兼容）
    /// - Parameter image: 待比较的图片
    /// - Returns: SSIM结果
    /// - Note: 此方法使用RunLoop等待异步计算完成，建议使用异步版本
    public func compareToOriginSync(with image: CGImage) throws -> SSIMResult {
        guard let originImage = originImage else {
            throw SSIMError.originNotSet
        }
        guard image.width == originWidth && image.height == originHeight else {
            throw SSIMError.imageSizeMismatch
        }

        return try calculateSync(from: originImage, to: image)
    }

    // MARK: - Resource Management

    /// 清理所有缓存和临时资源，用于内存优化
    /// 建议在处理大量图像或内存敏感场景时调用
    public func clearCaches() async {
        await asyncEngine.clearCaches()
        originImage = nil
        originWidth = 0
        originHeight = 0
        print("🧹 已清理所有缓存资源")
    }

    /// 同步版本的缓存清理
    public func clearCachesSync() {
        Task {
            await clearCaches()
        }
    }

    // MARK: - Performance and Diagnostics

    /// 获取性能统计信息
    public func getPerformanceStats() async -> String {
        return await asyncEngine.performanceStats
    }

    /// 获取硬件信息
    public func getHardwareInfo() async -> String {
        return await asyncEngine.hardwareInfo
    }

    /// 获取支持的优化特性
    public func getSupportedOptimizations() async -> [String] {
        return await asyncEngine.supportedOptimizations
    }

    /// 获取设备信息
    public var deviceInfo: String {
        return """
        设备名称: \(device.name)
        性能模式: \(performanceMode)
        最大线程组大小: \(device.maxThreadsPerThreadgroup)
        """
    }
}
