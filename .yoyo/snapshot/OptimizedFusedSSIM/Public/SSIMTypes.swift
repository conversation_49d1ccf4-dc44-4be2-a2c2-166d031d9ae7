//
//  SSIMTypes.swift
//  OptimizedFusedSSIM
//
//  Created by <PERSON><PERSON> on 2025/7/8.
//

import Foundation

/// SSIM计算的结果，包含SSIM值和图像尺寸信息
public struct SSIMResult {
    public let ssim: Double
    public let imageSize: ImageSize
}

/// 图像尺寸
public struct ImageSize {
    public let width: Int
    public let height: Int
}

/// SSIM模块可能抛出的错误类型
public enum SSIMError: Error {
    case imageSizeMismatch
    case originNotSet
    case metalKernelError(String)
}
