//
// MetalStateCache.swift
// OptimizedFusedSSIM
//
// 功能描述：重构的Metal状态缓存，支持动态管道状态缓存和线程组配置
// 优化阶段：第一阶段 - 核心架构重构
//

import Metal

// MARK: - 管道描述符

/// 管道状态描述符，用于缓存键
struct PipelineDescriptor: Hashable {
    let kernelName: String
    let threadgroupSize: MTLSize
    let constantValues: [String: Any]?

    init(kernelName: String, threadgroupSize: MTLSize = MTLSize(width: 16, height: 16, depth: 1), constantValues: [String: Any]? = nil) {
        self.kernelName = kernelName
        self.threadgroupSize = threadgroupSize
        self.constantValues = constantValues
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(kernelName)
        hasher.combine(threadgroupSize.width)
        hasher.combine(threadgroupSize.height)
        hasher.combine(threadgroupSize.depth)
        // 简化常量值的哈希处理
        if let constants = constantValues {
            hasher.combine(constants.count)
        }
    }

    static func == (lhs: PipelineDescriptor, rhs: PipelineDescriptor) -> Bool {
        return lhs.kernelName == rhs.kernelName &&
               lhs.threadgroupSize.width == rhs.threadgroupSize.width &&
               lhs.threadgroupSize.height == rhs.threadgroupSize.height &&
               lhs.threadgroupSize.depth == rhs.threadgroupSize.depth
    }
}

// MARK: - 重构的Metal状态缓存

/// 重构的Metal状态缓存，支持动态管道状态管理
internal final class MetalStateCache {

    /// Metal设备
    let device: MTLDevice

    /// 命令队列
    let commandQueue: MTLCommandQueue

    /// Metal着色器库
    let library: MTLLibrary

    /// 动态管道状态缓存
    private var pipelineStates: [PipelineDescriptor: MTLComputePipelineState] = [:]

    /// 预编译的核心管道状态
    let fusedSSIMPipelineState: MTLComputePipelineState
    let reductionPipelineState: MTLComputePipelineState

    /// 缓存统计
    private var cacheHitCount = 0
    private var cacheMissCount = 0

    /// 初始化重构的状态缓存
    /// - Parameter device: Metal设备（可选，如果不提供则使用系统默认设备）
    /// - Throws: 初始化失败时抛出错误
    init(device: MTLDevice? = nil) throws {
        // 使用提供的设备或系统默认设备
        if let providedDevice = device {
            self.device = providedDevice
        } else {
            guard let systemDevice = MTLCreateSystemDefaultDevice() else {
                throw SSIMError.metalKernelError("Metal is not supported on this device.")
            }
            self.device = systemDevice
        }

        guard let commandQueue = self.device.makeCommandQueue() else {
            throw SSIMError.metalKernelError("Failed to create MTLCommandQueue.")
        }
        self.commandQueue = commandQueue

        do {
            // 加载Metal库
            let bundle = Bundle(for: MetalStateCache.self)
            guard let libraryURL = bundle.url(forResource: "SSIMKernels", withExtension: "metallib") else {
                throw SSIMError.metalKernelError("SSIMKernels.metallib not found in the framework bundle.")
            }
            self.library = try self.device.makeLibrary(URL: libraryURL)

            // 预编译核心管道状态
            self.fusedSSIMPipelineState = try Self.createPipelineState(
                for: "fusedSSIMKernel",
                device: self.device,
                library: library
            )
            self.reductionPipelineState = try Self.createPipelineState(
                for: "reductionAverageKernel",
                device: self.device,
                library: library
            )

            print("✅ MetalStateCache 初始化成功 (设备: \(self.device.name))")

        } catch {
            if error is SSIMError {
                throw error
            } else {
                throw SSIMError.metalKernelError(
                    "Failed to initialize Metal state: \(error.localizedDescription)"
                )
            }
        }
    }

    // MARK: - 动态管道状态管理

    /// 获取或创建管道状态
    /// - Parameter descriptor: 管道描述符
    /// - Returns: 计算管道状态
    /// - Throws: 管道状态创建失败时抛出错误
    func getPipelineState(for descriptor: PipelineDescriptor) throws -> MTLComputePipelineState {
        // 检查缓存
        if let cached = pipelineStates[descriptor] {
            cacheHitCount += 1
            return cached
        }

        // 创建新的管道状态
        cacheMissCount += 1
        let pipelineState = try createPipelineState(for: descriptor)
        pipelineStates[descriptor] = pipelineState

        return pipelineState
    }

    /// 预编译常用管道状态
    /// - Parameter descriptors: 管道描述符列表
    func precompilePipelineStates(_ descriptors: [PipelineDescriptor]) {
        for descriptor in descriptors {
            do {
                _ = try getPipelineState(for: descriptor)
            } catch {
                print("⚠️ 预编译管道状态失败: \(descriptor.kernelName) - \(error)")
            }
        }
    }

    /// 清理管道状态缓存
    func clearPipelineCache() {
        pipelineStates.removeAll()
        cacheHitCount = 0
        cacheMissCount = 0
        print("🧹 管道状态缓存已清理")
    }

    /// 获取缓存统计信息
    var cacheStats: String {
        let totalRequests = cacheHitCount + cacheMissCount
        let hitRate = totalRequests > 0 ? Double(cacheHitCount) / Double(totalRequests) * 100 : 0
        return """
        缓存命中: \(cacheHitCount)
        缓存未命中: \(cacheMissCount)
        命中率: \(String(format: "%.1f", hitRate))%
        缓存大小: \(pipelineStates.count)
        """
    }

    // MARK: - 私有辅助方法

    /// 创建管道状态
    private func createPipelineState(for descriptor: PipelineDescriptor) throws -> MTLComputePipelineState {
        guard let kernelFunction = library.makeFunction(name: descriptor.kernelName) else {
            throw SSIMError.metalKernelError(
                "Kernel function '\(descriptor.kernelName)' not found in the library."
            )
        }

        do {
            return try device.makeComputePipelineState(function: kernelFunction)
        } catch {
            throw SSIMError.metalKernelError(
                "Failed to create compute pipeline state for '\(descriptor.kernelName)': \(error.localizedDescription)"
            )
        }
    }

    /// 静态辅助方法：创建计算管道状态
    private static func createPipelineState(
        for functionName: String,
        device: MTLDevice,
        library: MTLLibrary
    ) throws -> MTLComputePipelineState {
        guard let kernelFunction = library.makeFunction(name: functionName) else {
            throw SSIMError.metalKernelError(
                "Kernel function '\(functionName)' not found in the library."
            )
        }

        do {
            return try device.makeComputePipelineState(function: kernelFunction)
        } catch {
            throw SSIMError.metalKernelError(
                "Failed to create compute pipeline state for '\(functionName)': \(error.localizedDescription)"
            )
        }
    }
}

// MARK: - 扩展功能

extension MetalStateCache {
    /// 获取设备信息
    var deviceInfo: String {
        return """
        设备名称: \(device.name)
        最大线程组大小: \(device.maxThreadsPerThreadgroup)
        最大缓冲区长度: \(device.maxBufferLength)
        支持的GPU家族: \(getSupportedGPUFamilies())
        """
    }

    /// 获取支持的GPU家族列表
    private func getSupportedGPUFamilies() -> [String] {
        var families: [String] = []

        if device.supportsFamily(.apple8) { families.append("Apple8") }
        if device.supportsFamily(.apple7) { families.append("Apple7") }
        if device.supportsFamily(.apple6) { families.append("Apple6") }
        if device.supportsFamily(.apple5) { families.append("Apple5") }
        if device.supportsFamily(.common3) { families.append("Common3") }
        if device.supportsFamily(.common2) { families.append("Common2") }
        if device.supportsFamily(.common1) { families.append("Common1") }

        return families
    }
}
