//
//  MetalBridge.swift
//  OptimizedFusedSSIM
//
//  Created by <PERSON><PERSON> on 2025/7/7.
//

import Metal

/// A bridge to handle all low-level Metal interactions for SSIM calculation.
internal final class MetalBridge {

    private let stateCache: MetalStateCache

    // SSIM constants, using values for 8-bit images scaled to [0, 1]
    private var C1: Float = (0.01 * 1.0) * (0.01 * 1.0)  // (K1 * L)^2, L=1 for float images
    private var C2: Float = (0.03 * 1.0) * (0.03 * 1.0)  // (K2 * L)^2, L=1 for float images

    /// Initializes the Metal bridge.
    /// - Throws: Propagates errors from `MetalStateCache` initialization.
    init(stateCache: MetalStateCache) {
        self.stateCache = stateCache
    }

    /// Executes the full SSIM computation on the GPU.
    /// This method computes the SSIM for the R, G, and B channels separately and returns the weighted average.
    func execute(texture1: MTLTexture, texture2: MTLTexture) throws -> Float {
        let width = texture1.width
        let height = texture1.height
        let pixelCount = width * height

        // Compute each channel (R, G, B) separately.
        let ssimR = try computeChannel(
            texture1: texture1, texture2: texture2, channelIndex: 0, pixelCount: pixelCount)
        let ssimG = try computeChannel(
            texture1: texture1, texture2: texture2, channelIndex: 1, pixelCount: pixelCount)
        let ssimB = try computeChannel(
            texture1: texture1, texture2: texture2, channelIndex: 2, pixelCount: pixelCount)

        return min(ssimR, ssimG, ssimB)
    }

    /// Computes the SSIM for a single color channel.
    private func computeChannel(
        texture1: MTLTexture, texture2: MTLTexture, channelIndex: UInt32, pixelCount: Int
    ) throws -> Float {
        guard let commandBuffer = stateCache.commandQueue.makeCommandBuffer() else {
            throw SSIMError.metalKernelError("Failed to create MTLCommandBuffer.")
        }

        // Calculate number of threadgroups needed for result buffer sizing
        let threadsPerGroup = min(256, pixelCount)
        let threadGroupCount = (pixelCount + threadsPerGroup - 1) / threadsPerGroup

        // Buffers are now sized for a single channel computation.
        let ssimMapBuffer = stateCache.device.makeBuffer(
            length: MemoryLayout<Float>.stride * pixelCount, options: .storageModePrivate)
        let resultBuffer = stateCache.device.makeBuffer(
            length: MemoryLayout<Float>.stride * threadGroupCount, options: .storageModeShared)

        guard let ssimMapBuffer = ssimMapBuffer, let resultBuffer = resultBuffer else {
            throw SSIMError.metalKernelError(
                "Failed to create Metal buffers for channel \(channelIndex).")
        }

        // Initialize result buffer
        let resultPointer = resultBuffer.contents().bindMemory(
            to: Float.self, capacity: threadGroupCount)
        for i in 0..<threadGroupCount {
            resultPointer[i] = 0.0
        }

        // --- Pass 1: Fused SSIM Kernel ---
        try encodeFusedSSIMPass(
            commandBuffer: commandBuffer,
            texture1: texture1,
            texture2: texture2,
            ssimMapBuffer: ssimMapBuffer,
            channelIndex: channelIndex)

        // --- Pass 2: Reduction Average Kernel ---
        try encodeReductionPass(
            commandBuffer: commandBuffer,
            ssimMapBuffer: ssimMapBuffer,
            resultBuffer: resultBuffer,
            pixelCount: pixelCount)

        // Execute and wait
        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()

        if let error = commandBuffer.error {
            throw SSIMError.metalKernelError(
                "Command buffer execution failed for channel \(channelIndex): \(error.localizedDescription)"
            )
        }

        // Process and return result
        return try processResult(from: resultBuffer, pixelCount: pixelCount)
    }

    private func encodeFusedSSIMPass(
        commandBuffer: MTLCommandBuffer, texture1: MTLTexture, texture2: MTLTexture,
        ssimMapBuffer: MTLBuffer, channelIndex: UInt32
    ) throws {
        guard let encoder = commandBuffer.makeComputeCommandEncoder() else {
            throw SSIMError.metalKernelError("Failed to create compute encoder for fused pass.")
        }

        var mutableChannelIndex = channelIndex

        encoder.setComputePipelineState(stateCache.fusedSSIMPipelineState)
        encoder.setTexture(texture1, index: 0)
        encoder.setTexture(texture2, index: 1)
        encoder.setBuffer(ssimMapBuffer, offset: 0, index: 0)
        encoder.setBytes(&C1, length: MemoryLayout<Float>.stride, index: 1)
        encoder.setBytes(&C2, length: MemoryLayout<Float>.stride, index: 2)
        encoder.setBytes(&mutableChannelIndex, length: MemoryLayout<UInt32>.stride, index: 3)

        let tgSize = MTLSize(width: 16, height: 16, depth: 1)
        let gridSize = MTLSize(
            width: (texture1.width + tgSize.width - 1) / tgSize.width,
            height: (texture1.height + tgSize.height - 1) / tgSize.height,
            depth: 1)

        encoder.dispatchThreadgroups(gridSize, threadsPerThreadgroup: tgSize)
        encoder.endEncoding()
    }

    private func encodeReductionPass(
        commandBuffer: MTLCommandBuffer, ssimMapBuffer: MTLBuffer, resultBuffer: MTLBuffer,
        pixelCount: Int
    ) throws {
        guard let encoder = commandBuffer.makeComputeCommandEncoder() else {
            throw SSIMError.metalKernelError("Failed to create compute encoder for reduction pass.")
        }

        let pipelineState = stateCache.reductionPipelineState
        encoder.setComputePipelineState(pipelineState)

        // Configure threadgroup size and count
        let threadsPerGroup = min(pipelineState.maxTotalThreadsPerThreadgroup, 256)
        let threadGroupCount = (pixelCount + threadsPerGroup - 1) / threadsPerGroup
        let tgSize = MTLSize(width: threadsPerGroup, height: 1, depth: 1)
        let gridSize = MTLSize(width: threadGroupCount, height: 1, depth: 1)

        var count = UInt32(pixelCount)
        var numThreadgroups = UInt32(threadGroupCount)

        encoder.setBuffer(ssimMapBuffer, offset: 0, index: 0)
        encoder.setBuffer(resultBuffer, offset: 0, index: 1)
        encoder.setBytes(&count, length: MemoryLayout<UInt32>.stride, index: 2)
        encoder.setBytes(&numThreadgroups, length: MemoryLayout<UInt32>.stride, index: 3)
        encoder.dispatchThreadgroups(gridSize, threadsPerThreadgroup: tgSize)

        encoder.endEncoding()
    }

    private func processResult(from buffer: MTLBuffer, pixelCount: Int) throws -> Float {
        guard pixelCount > 0 else { return 0.0 }

        // The reduction kernel now outputs partial sums from each threadgroup
        // We need to sum all threadgroup results and then divide by pixel count
        let threadsPerGroup = min(256, pixelCount)
        let threadGroupCount = (pixelCount + threadsPerGroup - 1) / threadsPerGroup

        let resultPointer = buffer.contents().bindMemory(to: Float.self, capacity: threadGroupCount)

        var finalSum: Float = 0.0
        for i in 0..<threadGroupCount {
            finalSum += resultPointer[i]
        }

        return finalSum / Float(pixelCount)
    }

    // MARK: - Resource Management

    /// 清理缓存和临时资源
    func clearCaches() {
        // MetalBridge本身没有太多缓存，但可以清理StateCache
        // 这里可以添加future的缓存清理逻辑
    }
}
