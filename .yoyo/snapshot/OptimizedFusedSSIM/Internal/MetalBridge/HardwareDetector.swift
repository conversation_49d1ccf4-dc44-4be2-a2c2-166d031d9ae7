//
// HardwareDetector.swift
// OptimizedFusedSSIM
//
// 功能描述：硬件检测与自适应线程调度系统
// 优化阶段：第一阶段 - 核心架构重构
//

import Metal
import CoreGraphics

// MARK: - 硬件特性描述

/// 硬件配置文件，包含GPU的关键特性信息
struct HardwareProfile {
    let gpuFamily: MTLGPUFamily
    let maxThreadsPerGroup: Int
    let simdWidth: Int
    let recommendedOccupancy: Float
    let isAppleGPU: Bool
    let supportsHalfPrecision: Bool
    let maxBufferLength: Int
    
    /// 获取硬件特性的描述信息
    var description: String {
        return """
        GPU Family: \(gpuFamily)
        Max Threads Per Group: \(maxThreadsPerGroup)
        SIMD Width: \(simdWidth)
        Recommended Occupancy: \(recommendedOccupancy)
        Apple GPU: \(isAppleGPU)
        Half Precision: \(supportsHalfPrecision)
        Max Buffer Length: \(maxBufferLength)
        """
    }
}

// MARK: - MTLDevice 扩展

extension MTLDevice {
    /// 获取当前设备的硬件配置文件
    var hardwareProfile: HardwareProfile {
        let isApple = name.contains("Apple")
        let maxThreads = maxThreadsPerThreadgroup
        let simdWidth = isApple ? 32 : 64
        let recommendedOccupancy: Float = maxThreads >= 1024 ? 0.875 : 0.75
        
        return HardwareProfile(
            gpuFamily: detectGPUFamily(),
            maxThreadsPerGroup: maxThreads,
            simdWidth: simdWidth,
            recommendedOccupancy: recommendedOccupancy,
            isAppleGPU: isApple,
            supportsHalfPrecision: isApple,
            maxBufferLength: maxBufferLength
        )
    }
    
    /// 检测GPU家族
    private func detectGPUFamily() -> MTLGPUFamily {
        // 按照从新到旧的顺序检测
        if supportsFamily(.apple8) { return .apple8 }
        if supportsFamily(.apple7) { return .apple7 }
        if supportsFamily(.apple6) { return .apple6 }
        if supportsFamily(.apple5) { return .apple5 }
        if supportsFamily(.apple4) { return .apple4 }
        if supportsFamily(.apple3) { return .apple3 }
        if supportsFamily(.apple2) { return .apple2 }
        if supportsFamily(.apple1) { return .apple1 }
        
        // 非Apple GPU的通用家族
        if supportsFamily(.common3) { return .common3 }
        if supportsFamily(.common2) { return .common2 }
        return .common1
    }
}

// MARK: - 自适应线程配置

/// 自适应线程配置，根据硬件特性和图像尺寸计算最优线程分布
struct AdaptiveThreadConfig {
    let threadsPerThreadgroup: MTLSize
    let threadgroupsPerGrid: MTLSize
    let optimalOccupancy: Float
    let totalThreads: Int
    
    /// 计算给定设备和图像尺寸的最优线程配置
    /// - Parameters:
    ///   - device: Metal设备
    ///   - imageSize: 图像尺寸
    /// - Returns: 优化的线程配置
    static func optimal(for device: MTLDevice, imageSize: CGSize) -> AdaptiveThreadConfig {
        let profile = device.hardwareProfile
        let width = Int(imageSize.width)
        let height = Int(imageSize.height)
        
        // 计算最优线程组大小
        let baseSize = profile.simdWidth
        var threadsX = baseSize
        var threadsY = baseSize
        
        // 根据图像尺寸和硬件能力调整X维度
        while threadsX * 2 <= min(width, profile.maxThreadsPerGroup / threadsY) {
            threadsX *= 2
        }
        
        // 根据图像尺寸和硬件能力调整Y维度
        while threadsY * 2 <= min(height, profile.maxThreadsPerGroup / threadsX) {
            threadsY *= 2
        }
        
        // 确保线程组大小不超过硬件限制
        let maxThreadsPerGroup = profile.maxThreadsPerGroup
        while threadsX * threadsY > maxThreadsPerGroup {
            if threadsX > threadsY {
                threadsX /= 2
            } else {
                threadsY /= 2
            }
        }
        
        let threadgroupSize = MTLSize(width: threadsX, height: threadsY, depth: 1)
        let gridSize = MTLSize(
            width: (width + threadsX - 1) / threadsX,
            height: (height + threadsY - 1) / threadsY,
            depth: 1
        )
        
        let totalThreads = threadsX * threadsY * gridSize.width * gridSize.height
        
        return AdaptiveThreadConfig(
            threadsPerThreadgroup: threadgroupSize,
            threadgroupsPerGrid: gridSize,
            optimalOccupancy: profile.recommendedOccupancy,
            totalThreads: totalThreads
        )
    }
    
    /// 获取配置的描述信息
    var description: String {
        return """
        Threads Per Threadgroup: \(threadsPerThreadgroup.width)x\(threadsPerThreadgroup.height)
        Threadgroups Per Grid: \(threadgroupsPerGrid.width)x\(threadgroupsPerGrid.height)
        Total Threads: \(totalThreads)
        Optimal Occupancy: \(optimalOccupancy)
        """
    }
}

// MARK: - 硬件检测器

/// 硬件检测器，提供硬件特性检测和性能优化建议
class HardwareDetector {
    private let device: MTLDevice
    private let profile: HardwareProfile
    
    /// 初始化硬件检测器
    /// - Parameter device: Metal设备
    init(device: MTLDevice) {
        self.device = device
        self.profile = device.hardwareProfile
    }
    
    /// 获取硬件配置文件
    var hardwareProfile: HardwareProfile {
        return profile
    }
    
    /// 为给定图像尺寸计算最优线程配置
    /// - Parameter imageSize: 图像尺寸
    /// - Returns: 最优线程配置
    func optimalThreadConfig(for imageSize: CGSize) -> AdaptiveThreadConfig {
        return AdaptiveThreadConfig.optimal(for: device, imageSize: imageSize)
    }
    
    /// 检查设备是否支持高级优化特性
    /// - Returns: 支持的优化特性列表
    func supportedOptimizations() -> [String] {
        var optimizations: [String] = []
        
        if profile.isAppleGPU {
            optimizations.append("Apple GPU优化")
        }
        
        if profile.supportsHalfPrecision {
            optimizations.append("Half精度计算")
        }
        
        if profile.simdWidth >= 32 {
            optimizations.append("宽SIMD优化")
        }
        
        if profile.maxThreadsPerGroup >= 1024 {
            optimizations.append("大线程组优化")
        }
        
        return optimizations
    }
    
    /// 获取性能调优建议
    /// - Parameter imageSize: 目标图像尺寸
    /// - Returns: 性能调优建议
    func performanceTuningAdvice(for imageSize: CGSize) -> [String] {
        var advice: [String] = []
        let config = optimalThreadConfig(for: imageSize)
        
        let pixelCount = Int(imageSize.width * imageSize.height)
        
        if pixelCount < 256 * 256 {
            advice.append("小图像：建议使用CPU处理以避免GPU启动开销")
        } else if pixelCount > 4096 * 4096 {
            advice.append("大图像：建议分块处理以优化内存使用")
        }
        
        if config.threadsPerThreadgroup.width * config.threadsPerThreadgroup.height < profile.simdWidth {
            advice.append("线程组大小小于SIMD宽度，可能存在GPU利用率不足")
        }
        
        if profile.isAppleGPU && !profile.supportsHalfPrecision {
            advice.append("当前Apple GPU不支持Half精度，建议升级硬件以获得更好性能")
        }
        
        return advice
    }
}
