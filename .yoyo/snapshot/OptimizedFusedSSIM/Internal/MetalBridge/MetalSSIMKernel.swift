//
//  MetalSSIMKernel.swift
//  OptimizedFusedSSIM
//
//  Created by <PERSON><PERSON> on 2025/7/7.
//

import Foundation
import Metal

/// Metal SSIM计算核心，提供统一的SSIM计算接口
internal class MetalSSIMKernel {

  // MARK: - Core Components
  private let metalBridge: MetalBridge
  private let device: MTLDevice

  // MARK: - Initialization

  /// 初始化MetalSSIMKernel
  /// - Throws: Metal初始化相关错误
  public init() throws {
    do {
      let stateCache = try MetalStateCache()
      self.metalBridge = MetalBridge(stateCache: stateCache)
      self.device = stateCache.device
    } catch let error as SSIMError {
      throw error
    } catch {
      throw SSIMError.metalKernelError("MetalSSIMKernel初始化失败: \\(error.localizedDescription)")
    }
  }

  // MARK: - SSIM Calculation Interface

  /// 计算两张纹理之间的SSIM值 (带性能报告)
  /// - Parameters:
  ///   - texture1: 第一张图片的纹理
  ///   - texture2: 第二张图片的纹理
  /// - Returns: 一个元组，包含SSIM值和计算耗时
  public func calculateWithPerformance(from texture1: MTLTexture, to texture2: MTLTexture) throws
    -> (ssim: Double, time: Double)
  {
    let startTime = CFAbsoluteTimeGetCurrent()
    let ssimValue = try metalBridge.execute(texture1: texture1, texture2: texture2)
    let elapsedTime = (CFAbsoluteTimeGetCurrent() - startTime) * 1000
    return (Double(ssimValue), elapsedTime)
  }

  /// 计算两张纹理之间的SSIM值
  /// - Parameters:
  ///   - texture1: 第一张图片的纹理
  ///   - texture2: 第二张图片的纹理
  /// - Returns: SSIM值
  public func calculate(from texture1: MTLTexture, to texture2: MTLTexture) throws -> Double {
    return try calculateWithPerformance(from: texture1, to: texture2).ssim
  }

  // MARK: - Helper Methods

  private func createTexture(from data: [UInt8], width: Int, height: Int) throws -> MTLTexture {
    let descriptor = MTLTextureDescriptor.texture2DDescriptor(
      pixelFormat: .rgba8Unorm,
      width: width,
      height: height,
      mipmapped: false
    )
    descriptor.usage = [.shaderRead, .shaderWrite]
    descriptor.storageMode = .shared

    guard let texture = device.makeTexture(descriptor: descriptor) else {
      throw SSIMError.metalKernelError("无法创建Metal纹理")
    }

    let region = MTLRegion(
      origin: MTLOrigin(x: 0, y: 0, z: 0),
      size: MTLSize(width: width, height: height, depth: 1)
    )

    data.withUnsafeBytes { bytes in
      texture.replace(
        region: region,
        mipmapLevel: 0,
        withBytes: bytes.bindMemory(to: UInt8.self).baseAddress!,
        bytesPerRow: width * 4
      )
    }

    return texture
  }

  // MARK: - Resource Management

  /// 清理所有缓存和临时资源
  public func clearCaches() {
    // 清理Metal桥接器的缓存（如果有的话）
    metalBridge.clearCaches()
  }
}
